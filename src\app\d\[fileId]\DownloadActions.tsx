"use client";

import React from "react";

type Props = {
  expired: boolean;
  apiDownload: string;
};

export default function DownloadActions({ expired, apiDownload }: Props) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
      <a
        href={expired ? "#" : apiDownload}
        className={`inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium border transition ${
          expired
            ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
            : "bg-blue-600 text-white border-blue-600 hover:bg-blue-700"
        }`}
        {...(expired ? {} : { download: true })}
      >
        {expired ? "Fil udløbet" : "Download"}
      </a>

      <button
        className="inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium border border-gray-200 text-gray-700 bg-white hover:bg-gray-50"
        onClick={async () => {
          try {
            await navigator.clipboard.writeText(
              typeof window !== "undefined" ? window.location.href : ""
            );
          } catch {
            // no-op
          }
        }}
        type="button"
      >
        Kopiér side-link
      </button>
    </div>
  );
}