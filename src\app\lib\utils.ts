import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { UserPlan } from "./types"
import { PLAN_CONFIGS } from "./plans"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Format file size
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Format date to Danish format
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('da-DK', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date));
}

// Get plan display name
export function getPlanDisplayName(plan: UserPlan): string {
  const key = (plan in PLAN_CONFIGS ? plan : 'free') as keyof typeof PLAN_CONFIGS;
  return PLAN_CONFIGS[key].name;
}

// Calculate usage percentage
export function calculateUsagePercentage(used: number, limit: number): number {
  if (limit === 0) return 0;
  return Math.min((used / limit) * 100, 100);
}

// Generate random session ID for guest users
export function generateSessionId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

// Generate random file ID
export function generateFileId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Date.now().toString(36);
}

// Get file expiry date based on plan
export function getFileExpiryDate(plan: UserPlan): Date {
  const key = (plan in PLAN_CONFIGS ? plan : 'free') as keyof typeof PLAN_CONFIGS;
  const days = PLAN_CONFIGS[key].fileExpiry;
  const expiryDate = new Date();
  expiryDate.setDate(expiryDate.getDate() + days);
  return expiryDate;
}

// Check if user can upload based on plan limits
export function canUserUpload(
  plan: UserPlan,
  currentUsage: number,
  fileSize: number
): { canUpload: boolean; reason?: string } {
  const key = (plan in PLAN_CONFIGS ? plan : 'free') as keyof typeof PLAN_CONFIGS;
  const planConfig = PLAN_CONFIGS[key];
  const limit = planConfig.uploadLimit.amount;
  
  if (currentUsage + fileSize > limit) {
    return {
      canUpload: false,
      reason: `File would exceed ${formatFileSize(limit)} ${planConfig.uploadLimit.period} limit`
    };
  }
  
  return { canUpload: true };
}

// Get current period start date
export function getCurrentPeriodStart(period: 'week' | 'month'): Date {
  const now = new Date();
  
  if (period === 'week') {
    // Monday as start of week
    const monday = new Date(now);
    const day = monday.getDay();
    const diff = monday.getDate() - day + (day === 0 ? -6 : 1);
    monday.setDate(diff);
    monday.setHours(0, 0, 0, 0);
    return monday;
  } else {
    // First day of month
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    firstDay.setHours(0, 0, 0, 0);
    return firstDay;
  }
}

// Validate file type (basic validation)
export function isValidFileType(mimeType: string): boolean {
  // Allow all file types since GoFile.io accepts everything except folders
  // Only block empty or invalid MIME types
  return Boolean(mimeType && mimeType.trim() !== '');
}

// Get theme from localStorage or default
export function getStoredTheme(): 'light' | 'dark' | 'system' {
  if (typeof window === 'undefined') return 'system';
  return (localStorage.getItem('theme') as 'light' | 'dark' | 'system') || 'system';
}

// Set theme in localStorage
export function setStoredTheme(theme: 'light' | 'dark' | 'system') {
  if (typeof window === 'undefined') return;
  localStorage.setItem('theme', theme);
} 