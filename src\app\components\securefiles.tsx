"use client"

import { <PERSON><PERSON><PERSON><PERSON>, Lock, Database, EyeOff, Server, X } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useCallback, useEffect } from "react"

interface SecureFilesModalProps {
  isOpen: boolean
  onClose: () => void
}

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 },
}

// Use subtle spring for smooth, natural motion without jank.
// Keep transforms lightweight (opacity + translateY).
const panelVariants = {
  hidden: { opacity: 0, y: 16 },
  visible: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: 16 },
}

export default function SecureFiles({ isOpen, onClose }: SecureFilesModalProps) {
  // Prevent body scroll when modal is open (compute scrollbar width to avoid layout shift)
  useEffect(() => {
    if (!isOpen) return
    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth
    const prevOverflow = document.body.style.overflow
    const prevPadding = document.body.style.paddingRight
    document.body.style.overflow = "hidden"
    document.body.style.paddingRight = `${scrollbarWidth}px`
    return () => {
      document.body.style.overflow = prevOverflow
      document.body.style.paddingRight = prevPadding
    }
  }, [isOpen])

  // Close on Escape
  useEffect(() => {
    if (!isOpen) return
    const onKey = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        e.preventDefault()
        onClose()
      }
    }
    window.addEventListener("keydown", onKey)
    return () => window.removeEventListener("keydown", onKey)
  }, [isOpen, onClose])

  const handleBackdropClick = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      // Only close if clicking the backdrop itself
      if (e.target === e.currentTarget) onClose()
    },
    [onClose]
  )

  const securityFeatures = [
    {
      icon: <Lock className="h-5 w-5" />,
      title: "Kryptering under transport og opbevaring",
      description: "Alle filer krypteres med TLS under overførsel og AES-256 ved opbevaring.",
    },
    {
      icon: <Database className="h-5 w-5" />,
      title: "Ingen dataanalyse eller AI-træning",
      description: "Vi analyserer ikke dine filers indhold og bruger dem ikke til at træne AI-modeller.",
    },
    {
      icon: <EyeOff className="h-5 w-5" />,
      title: "Ingen salg til tredjeparter",
      description: "Vi deler eller sælger ikke dine data med nogen tredjepart - aldrig.",
    },
    {
      icon: <Server className="h-5 w-5" />,
      title: "Data i Europa",
      description: "Alle data opbevares i Europa og er underlagt europæisk lovgivning.",
    },
    {
      icon: <ShieldCheck className="h-5 w-5" />,
      title: "Automatisk sletning",
      description:
        "Filers levetid styres af din plan, og de slettes automatisk efter udløb. Vi kan beholde dine filer op til 3 dage efter udløb.",
    },
  ]

  return (
    <AnimatePresence mode="wait">
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          transition={{ duration: 0.22, ease: [0.22, 1, 0.36, 1] }}
          aria-modal="true"
          role="dialog"
        >
          {/* Clickable overlay to close when clicking outside panel */}
          <button
            type="button"
            aria-label="Luk dialog (klik udenfor)"
            onClick={onClose}
            className="fixed inset-0 bg-black/60"
          />

          {/* Panel: smooth spring for open/close; no scale to avoid repaints */}
          <motion.div
            className="relative w-full max-w-xl mx-auto rounded-2xl border border-gray-200 bg-white shadow-2xl z-10 max-h-[90vh] overflow-y-auto will-change-transform will-change-opacity"
            variants={panelVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            transition={{
              type: "spring",
              stiffness: 380,
              damping: 32,
              mass: 0.7,
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="sticky top-0 z-20 flex items-center justify-between px-6 py-5 border-b bg-white/95 backdrop-saturate-150">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-blue-100 text-blue-600 grid place-items-center transition-transform duration-200 ease-out group-hover:rotate-1">
                  <ShieldCheck className="h-6 w-6" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Sikkerhed og privatliv</h2>
                  <p className="text-sm text-gray-600">Sådan beskytter vi dine filer og data</p>
                </div>
              </div>

              <button
                onClick={onClose}
                className="group inline-flex items-center justify-center rounded-full p-2.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-150"
                aria-label="Luk"
              >
                <X className="h-5 w-5 transition-transform duration-150 group-hover:scale-110" />
              </button>
            </div>

            {/* Content */}
            <div className="px-6 py-6">
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {securityFeatures.map((feature, idx) => (
                  <li
                    key={idx}
                    className="group flex items-start gap-3 rounded-xl border border-gray-100 bg-white p-4 shadow-sm transition-all duration-200 ease-out hover:-translate-y-0.5 hover:shadow-md"
                  >
                    <div className="p-2.5 rounded-lg bg-blue-50 text-blue-600 shrink-0 transition-transform duration-200 ease-out group-hover:-translate-y-0.5">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{feature.title}</h3>
                      <p className="mt-1.5 text-sm text-gray-600">{feature.description}</p>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
