import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/app/lib/auth'
import { getUsersCollection } from '@/app/lib/mongodb'
import { PLAN_CONFIGS } from '@/app/lib/plans'

export async function GET(request: NextRequest) {
  try {
    const session = await auth()

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const users = await getUsersCollection()
    const user = await users.findOne({ email: session.user.email })

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Normalize plan to a valid key (support aliases like 'basis'/'pro')
    const rawPlan = String(user.plan || 'free').toLowerCase()
    const PLAN_ALIASES: Record<string, keyof typeof PLAN_CONFIGS> = {
      guest: 'guest',
      free: 'free',
      gratis: 'free',
      basis: 'upgrade1',
      basic: 'upgrade1',
      upgrade1: 'upgrade1',
      pro: 'upgrade2',
      upgrade2: 'upgrade2'
    }
    const userPlan = (PLAN_ALIASES[rawPlan] ?? (rawPlan in PLAN_CONFIGS ? (rawPlan as keyof typeof PLAN_CONFIGS) : 'free')) as keyof typeof PLAN_CONFIGS
    const planConfig = PLAN_CONFIGS[userPlan]

    if (!planConfig) {
      return NextResponse.json(
        { success: false, error: 'Invalid plan configuration' },
        { status: 500 }
      )
    }

    // Calculate usage based on plan period
    const usage = user.usage || { monthly: 0, weekly: 0 }
    let currentUsage = 0
    let period = 'månedlig'

    if (planConfig.uploadLimit.period === 'uge') {
      currentUsage = usage.weekly || 0
      period = 'ugentlig'
    } else if (planConfig.uploadLimit.period === 'måned') {
      currentUsage = usage.monthly || 0
      period = 'månedlig'
    } else {
      // Session-based (guest)
      currentUsage = usage.session || 0
      period = 'session'
    }

    const usagePercentage = (currentUsage / planConfig.uploadLimit.amount) * 100

    return NextResponse.json({
      success: true,
      data: {
        current: currentUsage,
        limit: planConfig.uploadLimit.amount,
        percentage: Math.min(usagePercentage, 100),
        period: period,
        plan: userPlan
      }
    })

  } catch (error) {
    console.error('Error fetching usage:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}