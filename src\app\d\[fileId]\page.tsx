import { notFound } from 'next/navigation'
import Link from 'next/link'
import { getFilesCollection } from '@/app/lib/mongodb'
import { type FileRecord } from '@/app/lib/types'
import { ObjectId } from 'mongodb'

export default async function DownloadPage({
  params,
}: {
  params: { fileId: string }
}) {
  const { fileId } = params
  const files = await getFilesCollection()

  // Prefer public filename for shared links
  let file = await files.findOne({ filename: fileId, isActive: true }) as unknown as FileRecord | null

  // Fallback: allow direct _id usage (older copied links)
  if (!file && ObjectId.isValid(fileId)) {
    const byId = await files.findOne({ _id: new ObjectId(fileId), isActive: true }) as unknown as FileRecord | null
    if (byId) file = byId
  }

  if (!file) {
    notFound()
  }

  // Always use public filename for API download/logging when available
  const apiId = file!.filename
  const apiDownload = `/api/download/${apiId}`

  const sizeFormatter = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`
  }

  const expired = new Date() > new Date(file!.expiryDate)

  return (
    <main className="min-h-[70vh] bg-gradient-to-b from-gray-50 to-gray-100">
      <section className="relative py-14">
        <div className="max-w-2xl mx-auto px-4 sm:px-6">
          <div className="rounded-2xl border bg-white shadow-sm p-6 sm:p-8">
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900">Download fil</h1>
              <p className="text-gray-600 mt-1">Sikker offentlig downloadside</p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">{file!.originalName}</div>
                  <div className="text-sm text-gray-500">{file!.mimeType}</div>
                </div>
                <div className="text-sm text-gray-700">{sizeFormatter(file!.size)}</div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <a
                  href={expired ? '#' : apiDownload}
                  className={`inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium border transition ${
                    expired
                      ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                      : 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700'
                  }`}
                  {...(expired ? {} : { download: true })}
                >
                  {expired ? 'Fil udløbet' : 'Download'}
                </a>

                {/* Use a standard anchor for sharing; avoid client-only Clipboard API in Server Component tree */}
                <a
                  className="inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium border border-gray-200 text-gray-700 bg-white hover:bg-gray-50"
                  href={typeof window !== 'undefined' ? window.location.href : '#'}
                  onClick={(e) => {
                    e.preventDefault()
                    if (typeof window !== 'undefined' && navigator?.clipboard) {
                      navigator.clipboard.writeText(window.location.href).catch(() => {})
                    }
                  }}
                >
                  Kopiér side-link
                </a>
              </div>

              <div className="text-xs text-gray-500">
                Udløbsdato: {new Date(file!.expiryDate).toLocaleString('da-DK')}
              </div>

              {file!.downloadLimit !== -1 && (
                <div className="text-xs text-gray-500">
                  Downloads tilbage: {Math.max(0, file!.downloadLimit - (file!.downloadCount ?? 0))}
                </div>
              )}
            </div>

            <div className="mt-6 text-center">
              <Link href="/" className="text-sm text-blue-700 hover:underline">
                Gå til forsiden
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}