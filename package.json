{"name": "flyfiles", "version": "1.0.0", "description": "Danish file sharing platform similar to WeTransfer", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@types/react-icons": "^2.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.460.0", "mongodb": "^6.10.0", "next": "15.4.5", "next-auth": "5.0.0-beta.25", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "tailwind-merge": "^2.5.4", "terabox-upload-tool": "^1.3.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}, "keywords": ["file-sharing", "danish", "nextjs", "typescript", "mongodb", "wetransfer-clone"], "author": "FlyFiles Team", "license": "MIT"}