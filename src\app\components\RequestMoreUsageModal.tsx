"use client";

import React, { useEffect, useCallback, useRef, useState } from "react";
import { TrendingUp, X } from "lucide-react";
import { Button } from "../components/ui/button";
import { useSession } from "next-auth/react";
import { PLAN_CONFIGS } from "@/app/lib/plans";

export type RequestMoreUsageModalProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (amount: string, message: string) => void;
  submitting?: boolean;
  amount: string;
  message: string;
  setAmount: (v: string) => void;
  setMessage: (v: string) => void;
  onResetForm?: () => void;
};

/**
 * RequestMoreUsageModal
 * Controlled modal component for requesting additional usage.
 * Enhancements:
 * - Smooth close/open animation via mount-preserving isVisible state
 * - Backdrop and panel have longer, eased transitions
 * - Body scroll lock avoids layout shift
 */
export default function RequestMoreUsageModal({
  open,
  onOpenChange,
  onSubmit,
  submitting = false,
  amount,
  message,
  setAmount,
  setMessage,
  onResetForm,
}: RequestMoreUsageModalProps) {
  const [isVisible, setIsVisible] = useState(open);
  const closeTimeoutRef = useRef<number | null>(null);

  const close = useCallback(() => {
    onOpenChange(false);
    onResetForm?.();
  }, [onOpenChange, onResetForm]);

  // Manage mount visibility to allow exit animation before unmounting interactions
  useEffect(() => {
    if (open) {
      if (closeTimeoutRef.current) {
        window.clearTimeout(closeTimeoutRef.current);
        closeTimeoutRef.current = null;
      }
      setIsVisible(true);
    } else {
      // keep visible for exit animation, then disable pointer events
      closeTimeoutRef.current = window.setTimeout(() => {
        setIsVisible(false);
      }, 280); // should match panel transition duration
    }
    return () => {
      if (closeTimeoutRef.current) {
        window.clearTimeout(closeTimeoutRef.current);
        closeTimeoutRef.current = null;
      }
    };
  }, [open]);

  // Escape to close
  useEffect(() => {
    if (!open) return;
    const onKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        e.preventDefault();
        close();
      }
    };
    document.addEventListener("keydown", onKeyDown);
    return () => document.removeEventListener("keydown", onKeyDown);
  }, [open, close]);

  // Smooth, layout-stable scroll lock
  useEffect(() => {
    const body = document.body;
    if (!open) {
      body.style.overflow = "";
      body.style.paddingRight = "";
      body.classList.remove("scroll-locked");
      return;
    }
    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
    body.classList.add("scroll-locked");
    if (scrollbarWidth > 0) {
      body.style.paddingRight = `${scrollbarWidth}px`;
    } else {
      body.style.paddingRight = "";
    }
    body.style.overflow = "hidden";
    return () => {
      body.style.overflow = "";
      body.style.paddingRight = "";
      body.classList.remove("scroll-locked");
    };
  }, [open]);

  // Easing presets
  const overlayEase = "ease-[cubic-bezier(0.22,1,0.36,1)]";
  const panelEase = "ease-[cubic-bezier(0.22,1,0.36,1)]";

  return (
    <div
      className={`fixed inset-0 z-50 ${isVisible ? "pointer-events-auto" : "pointer-events-none"}`}
      aria-modal="true"
      role="dialog"
      aria-hidden={!open}
    >
      {/* Overlay */}
      <div
        className={`fixed inset-0 bg-black/60 transition-opacity duration-300 ${overlayEase} ${
          open ? "opacity-100" : "opacity-0"
        }`}
      />

      {/* Wrapper catches outside clicks */}
      <div className="relative z-10 flex min-h-full items-center justify-center p-4" onClick={close}>
        {/* Panel */}
        <div
          className={`
            relative w-full max-w-xl mx-auto rounded-2xl border border-gray-200 bg-white shadow-2xl max-h-[90vh] overflow-y-auto
            will-change-transform will-change-opacity
            transition-[opacity,transform] duration-300 ${panelEase}
            ${open ? "opacity-100 translate-y-0 scale-100" : "opacity-0 translate-y-3 scale-[0.98]"}
          `}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="sticky top-0 z-20 flex items-center justify-between px-6 py-5 border-b bg-white/95 backdrop-saturate-150">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-full bg-blue-100 text-blue-600 grid place-items-center">
                <TrendingUp className="h-6 w-6" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Anmod om mere forbrug</h2>
                <p className="text-sm text-gray-600">Fortæl os hvad du har brug for</p>

                {/* Current plan notice */}
                <CurrentPlanNotice />
              </div>
            </div>

            <button
              onClick={close}
              className="group inline-flex items-center justify-center rounded-full p-2.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-150"
              aria-label="Luk"
            >
              <X className="h-5 w-5 transition-transform duration-150 group-hover:scale-110" />
            </button>
          </div>

          {/* Content */}
          <div className="px-6 py-6 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Hvor meget ekstra har du brug for? (MB/GB)</label>
              <input
                type="text"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="Fx 2 GB"
                className="w-full rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm outline-none focus:ring-2 focus:ring-blue-600"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Besked</label>
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Beskriv kort hvorfor du har brug for mere forbrug..."
                className="w-full min-h-[110px] rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm outline-none focus:ring-2 focus:ring-blue-600"
              />
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end gap-3 px-6 py-5 border-t">
            <Button variant="ghost" onClick={close}>
              Annuller
            </Button>
            <Button onClick={() => onSubmit(amount, message)} disabled={submitting || (!amount && !message)} className="bg-blue-600 hover:bg-blue-700">
              {submitting ? "Sender..." : "Send anmodning"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

/** Shows the user's current plan inside the modal header */
function CurrentPlanNotice() {
  const { data: session } = useSession();

  // Normalize possible plan strings from session to PLAN_CONFIGS keys
  const rawPlan = String((session?.user as any)?.plan || 'free').toLowerCase();
  const PLAN_ALIASES: Record<string, keyof typeof PLAN_CONFIGS> = {
    guest: 'guest',
    free: 'free',
    gratis: 'free',
    basis: 'upgrade1',
    basic: 'upgrade1',
    upgrade1: 'upgrade1',
    pro: 'upgrade2',
    upgrade2: 'upgrade2',
  };
  const planKey = (PLAN_ALIASES[rawPlan] ?? (rawPlan in PLAN_CONFIGS ? (rawPlan as keyof typeof PLAN_CONFIGS) : 'free')) as keyof typeof PLAN_CONFIGS;
  const plan = PLAN_CONFIGS[planKey] ?? PLAN_CONFIGS.free;

  return (
    <div className="mt-2">
      <span className="inline-flex items-center gap-2 rounded-full border border-gray-200 bg-gray-50 px-3 py-1 text-xs text-gray-700">
        <span className="h-2 w-2 rounded-full bg-emerald-500" />
        Nuværende plan: <strong className="font-semibold">{plan.name}</strong>
      </span>
    </div>
  );
}