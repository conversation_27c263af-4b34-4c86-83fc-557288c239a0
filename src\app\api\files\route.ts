import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/app/lib/auth'
import { getFilesCollection, getUsersCollection } from '@/app/lib/mongodb'
import { PLAN_CONFIGS } from '@/app/lib/plans'
import { 
  generateFileId, 
  getFileExpiryDate, 
  canUserUpload, 
  isValidFileType,
  getCurrentPeriodStart
} from '@/app/lib/utils'
import { ApiResponse, FileRecord, FileUploadRequest } from '@/app/lib/types'
import { ObjectId } from 'mongodb'

// GET - List user's files
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' } as ApiResponse,
        { status: 401 }
      )
    }

    const users = await getUsersCollection()
    const user = await users.findOne({ email: session.user.email })
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' } as ApiResponse,
        { status: 404 }
      )
    }

    const files = await getFilesCollection()
    const userFiles = await files
      .find({ 
        ownerId: user._id.toString(),
        isActive: true 
      })
      .sort({ uploadDate: -1 })
      .toArray()

    return NextResponse.json({
      success: true,
      data: userFiles as unknown as FileRecord[]
    } as ApiResponse<FileRecord[]>)

  } catch (error) {
    console.error('Error getting files:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    )
  }
}

// POST - Create new file record (metadata only)
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' } as ApiResponse,
        { status: 401 }
      )
    }

    const body: FileUploadRequest = await request.json()
    
    // Validate request
    if (!body.filename || !body.size || !body.mimeType) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' } as ApiResponse,
        { status: 400 }
      )
    }

    // Validate file type
    if (!isValidFileType(body.mimeType)) {
      return NextResponse.json(
        { success: false, error: 'File type not allowed' } as ApiResponse,
        { status: 400 }
      )
    }

    const users = await getUsersCollection()
    const user = await users.findOne({ email: session.user.email })
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' } as ApiResponse,
        { status: 404 }
      )
    }

    // Check upload limits
    const planConfig = PLAN_CONFIGS[user.plan as keyof typeof PLAN_CONFIGS]
    const currentUsage = user.plan === 'free' ? user.usage.monthly : user.usage.weekly
    const uploadCheck = canUserUpload(user.plan, currentUsage, body.size)
    
    if (!uploadCheck.canUpload) {
      return NextResponse.json(
        { success: false, error: uploadCheck.reason } as ApiResponse,
        { status: 400 }
      )
    }

    // Create file record
    const fileId = generateFileId()
    const expiryDate = getFileExpiryDate(user.plan)
    
    const fileRecord: Omit<FileRecord, '_id'> = {
      filename: fileId,
      originalName: body.filename,
      mimeType: body.mimeType,
      size: body.size,
      ownerId: user._id.toString(),
      uploadDate: new Date(),
      expiryDate: expiryDate,
      downloadCount: 0,
      downloadLimit: body.downloadLimit || (planConfig.downloadLimits.unlimited ? -1 : 10),
      isActive: true
    }

    const files = await getFilesCollection()
    const result = await files.insertOne(fileRecord)

    // Update user usage
    const usageField = user.plan === 'free' ? 'usage.monthly' : 'usage.weekly'
    await users.updateOne(
      { _id: user._id },
      { $inc: { [usageField]: body.size } }
    )

    const createdFile = await files.findOne({ _id: result.insertedId })

    return NextResponse.json({
      success: true,
      data: createdFile as unknown as FileRecord,
      message: 'File metadata created successfully'
    } as ApiResponse<FileRecord>)

  } catch (error) {
    console.error('Error creating file:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    )
  }
} 