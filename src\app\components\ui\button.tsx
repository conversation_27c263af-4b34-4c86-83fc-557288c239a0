import * as React from "react"
import { cn } from "@/app/lib/utils"

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', ...props }, ref) => {
    return (
      <button
        className={cn(
          "group relative inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-bold ring-offset-background transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 overflow-hidden",
          {
            'default': "bg-gradient-to-b from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 hover:shadow-xl hover:shadow-blue-500/40 hover:scale-105",
            'destructive': "bg-gradient-to-b from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 hover:shadow-xl hover:shadow-red-500/40 hover:scale-105",
            'outline': "border-2 border-blue-600 bg-transparent text-blue-600 hover:bg-gradient-to-b hover:from-blue-600 hover:to-blue-700 hover:text-white hover:shadow-xl hover:shadow-blue-500/40 hover:scale-105 hover:border-blue-700",
            'secondary': "bg-gradient-to-b from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 hover:shadow-xl hover:scale-105",
            'ghost': "hover:bg-gradient-to-b hover:from-blue-50 hover:to-blue-100 text-gray-900 hover:text-blue-600 hover:scale-105",
            'link': "text-blue-600 underline-offset-4 hover:underline hover:text-blue-700"
          }[variant],
          {
            'default': "h-10 px-4 py-2",
            'sm': "h-9 rounded-lg px-3",
            'lg': "h-12 rounded-xl px-8 text-base",
            'icon': "h-10 w-10"
          }[size],
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button } 