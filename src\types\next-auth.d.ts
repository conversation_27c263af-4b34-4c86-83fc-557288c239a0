import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      plan?: string;
      usage?: {
        monthly: number;
        weekly: number;
        session?: number;
      };
    };
  }

  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    plan?: string;
    usage?: {
      monthly: number;
      weekly: number;
      session?: number;
    };
  }
}
