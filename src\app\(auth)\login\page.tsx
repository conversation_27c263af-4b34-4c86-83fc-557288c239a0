"use client"

import { useEffect } from "react"
import { useSession, signIn } from "next-auth/react"
import { useRouter } from "next/navigation"
import { Button } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import Link from "next/link"

export default function LoginPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  // Redirect authenticated users to dashboard
  useEffect(() => {
    if (session) {
      router.push('/dashboard')
    }
  }, [session, router])

  // Loading state matches app brand color
  if (status === "loading") {
    return (
      <div className="min-h-[70vh] flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
      </div>
    )
  }

  if (session) return null

  return (
    <section
      aria-labelledby="login-title"
      className="relative pt-28 pb-16 bg-white"
    >
      {/* Subtle gradient background stripe to match branding */}
      <div className="pointer-events-none absolute inset-x-0 top-0 h-40 bg-gradient-to-b from-blue-50 to-transparent"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-10">
          <div className="mx-auto h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center shadow-lg shadow-blue-500/30">
            <span className="text-white font-bold text-xl">F</span>
          </div>
          <h1 id="login-title" className="mt-6 text-3xl sm:text-4xl font-extrabold tracking-tight text-gray-900">
            Log ind på FlyFiles
          </h1>
          <p className="mt-2 text-sm sm:text-base text-gray-600">
            Få adgang til 15GB månedlig upload, længere opbevaring og flere funktioner
          </p>
        </div>

        {/* Content */}
        <div className="mx-auto max-w-xl">
          <Card className="overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100">
              <CardTitle className="text-gray-900">Vælg login metode</CardTitle>
              <CardDescription className="text-gray-600">
                Vi bruger Google OAuth for sikker og nem adgang
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              <Button
                onClick={() => signIn('google')}
                className="w-full flex items-center justify-center space-x-2"
                size="lg"
              >
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/3c/Google_Favicon_2025.svg/120px-Google_Favicon_2025.svg.png"
                  alt="Google logo"
                  className="h-5 w-5"
                />
                <span>Fortsæt med Google</span>
              </Button>

              <p className="text-center text-xs text-gray-500">
                Ved at logge ind accepterer du vores{" "}
                <Link href="/tos" className="text-blue-600 hover:text-blue-700 underline underline-offset-2">
                  Servicevilkår
                </Link>{" "}
                og{" "}
                <Link href="/privatlivspolitik" className="text-blue-600 hover:text-blue-700 underline underline-offset-2">
                  Privatlivspolitik
                </Link>
                .
              </p>
            </CardContent>
          </Card>

          {/* Benefits */}
          <Card className="mt-6">
            <CardContent className="p-6">
              <h3 className="font-semibold mb-3 text-gray-900">Fordele ved at logge ind</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 15GB månedlig upload (vs. 250MB som gæst)</li>
                <li>• 10 dages fil-opbevaring (vs. 7 dage)</li>
                <li>• Konfigurerbare download-grænser</li>
                <li>• Filhistorik og statistikker</li>
                <li>• Mulighed for upgrade til større planer</li>
              </ul>
            </CardContent>
          </Card>

          {/* Back link */}
          <div className="text-center mt-6">
            <Link
              href="/"
              className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700"
            >
              <svg className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
              </svg>
              Tilbage til forsiden
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}