import { NextResponse, type NextRequest } from 'next/server'
import { getFilesCollection, getDownloadLogsCollection } from '@/app/lib/mongodb'
import { ApiResponse } from '@/app/lib/types'

export async function GET(
  request: NextRequest,
  context: any
) {
  try {
    const { fileId } = (context as { params?: { fileId?: string } }).params ?? {}
    
    if (!fileId) {
      return NextResponse.json(
        { success: false, error: 'File ID required' } as ApiResponse,
        { status: 400 }
      )
    }

    const files = await getFilesCollection()
    // Accept either public filename (preferred) or Mongo _id for backward compatibility
    let file = await files.findOne({ filename: fileId, isActive: true })
    if (!file && typeof fileId === 'string') {
      try {
        const { ObjectId } = await import('mongodb')
        if (ObjectId.isValid(fileId)) {
          const byId = await files.findOne({ _id: new ObjectId(fileId), isActive: true })
          if (byId) file = byId
        }
      } catch {
        // ignore
      }
    }
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'File not found or expired' } as ApiResponse,
        { status: 404 }
      )
    }

    // Check if file has expired
    if (new Date() > new Date(file.expiryDate)) {
      // Mark file as inactive
      await files.updateOne(
        { _id: file._id },
        { $set: { isActive: false } }
      )
      
      return NextResponse.json(
        { success: false, error: 'File has expired' } as ApiResponse,
        { status: 410 }
      )
    }

    // Check download limits
    if (file.downloadLimit !== -1 && file.downloadCount >= file.downloadLimit) {
      return NextResponse.json(
        { success: false, error: 'Download limit reached' } as ApiResponse,
        { status: 429 }
      )
    }

    // Log the download
    const downloadLogs = await getDownloadLogsCollection()
    const userAgent = request.headers.get('user-agent') || 'Unknown'
    const forwarded = request.headers.get('x-forwarded-for')
    const ip = forwarded ? forwarded.split(',')[0] : 'unknown'

    await downloadLogs.insertOne({
      fileId: file._id.toString(),
      downloadedAt: new Date(),
      ip: ip,
      userAgent: userAgent
    })

    // Increment download count
    await files.updateOne(
      { _id: file._id },
      { $inc: { downloadCount: 1 } }
    )

    // If storage is not yet integrated, still return metadata but provide stable page URL
    const publicId = file.filename
    const pageUrl = `${request.nextUrl.origin}/d/${publicId}`

    return NextResponse.json({
      success: true,
      data: {
        filename: file.originalName,
        size: file.size,
        mimeType: file.mimeType,
        downloadUrl: pageUrl, // Link to the public download page
        expiryDate: file.expiryDate,
        downloadsRemaining: file.downloadLimit === -1 ? 'unlimited' : file.downloadLimit - file.downloadCount - 1
      },
      message: 'File download ready - visit the public page to download'
    } as ApiResponse)

  } catch (error) {
    console.error('Error processing download:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    )
  }
}

// HEAD request for checking file availability without logging download
export async function HEAD(
  request: NextRequest,
  context: any
) {
  try {
    const { fileId } = (context as { params?: { fileId?: string } }).params ?? {}
    
    const files = await getFilesCollection()
    let file = await files.findOne({ filename: fileId, isActive: true })
    if (!file && typeof fileId === 'string') {
      try {
        const { ObjectId } = await import('mongodb')
        if (ObjectId.isValid(fileId)) {
          const byId = await files.findOne({ _id: new ObjectId(fileId), isActive: true })
          if (byId) file = byId
        }
      } catch {
        // ignore
      }
    }
    
    if (!file || new Date() > new Date(file.expiryDate)) {
      return new NextResponse(null, { status: 404 })
    }

    return new NextResponse(null, { 
      status: 200,
      headers: {
        'Content-Length': file.size.toString(),
        'Content-Type': file.mimeType,
        'Cache-Control': 'no-cache'
      }
    })

  } catch (error) {
    return new NextResponse(null, { status: 500 })
  }
} 