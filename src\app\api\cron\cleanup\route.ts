import { NextRequest, NextResponse } from 'next/server'
import { getFilesCollection, getSessionsCollection } from '@/app/lib/mongodb'
import { ApiResponse } from '@/app/lib/types'

export async function POST(request: NextRequest) {
  try {
    // This endpoint should be called by a cron job or scheduled task
    // Verify request is from authorized source (you might want to add API key authentication)
    
    const now = new Date()
    
    // Clean up expired files
    const files = await getFilesCollection()
    const expiredFiles = await files.find({
      isActive: true,
      expiryDate: { $lt: now }
    }).toArray()

    if (expiredFiles.length > 0) {
      // Mark files as inactive
      await files.updateMany(
        { expiryDate: { $lt: now }, isActive: true },
        { $set: { isActive: false } }
      )

      // In a real implementation, you would also delete the actual files from storage
      console.log(`Marked ${expiredFiles.length} files as expired`)
    }

    // Clean up expired guest sessions
    const sessions = await getSessionsCollection()
    const expiredSessions = await sessions.find({
      expiresAt: { $lt: now }
    }).toArray()

    if (expiredSessions.length > 0) {
      // Delete expired sessions and their associated files
      const sessionIds = expiredSessions.map(session => session.sessionId)
      
      await files.updateMany(
        { sessionId: { $in: sessionIds }, isActive: true },
        { $set: { isActive: false } }
      )

      await sessions.deleteMany({
        expiresAt: { $lt: now }
      })

      console.log(`Cleaned up ${expiredSessions.length} expired guest sessions`)
    }

    return NextResponse.json({
      success: true,
      data: {
        expiredFiles: expiredFiles.length,
        expiredSessions: expiredSessions.length,
        cleanupTime: now.toISOString()
      },
      message: 'Cleanup completed successfully'
    } as ApiResponse)

  } catch (error) {
    console.error('Error during cleanup:', error)
    return NextResponse.json(
      { success: false, error: 'Cleanup failed' } as ApiResponse,
      { status: 500 }
    )
  }
}

// Only allow POST requests
export async function GET() {
  return NextResponse.json(
    { success: false, error: 'Method not allowed' } as ApiResponse,
    { status: 405 }
  )
} 